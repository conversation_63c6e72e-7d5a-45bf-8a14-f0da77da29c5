#!/bin/bash

# Configuration
RESOURCE_GROUP="volks-loadtest-rg"
STORAGE_ACCOUNT="volksloadtestsa"
CONTAINER_NAME="volks-loadtest-blob"
SCRIPT_NAME="assign-agent.sh"
CONTAINER_SAS_TOKEN="sp=r&st=2025-07-18T01:17:41Z&se=2025-07-18T09:32:41Z&spr=https&sv=2024-11-04&sr=c&sig=Yu%2FgbENBHqODTJIyDfYJMyDH5xLtl7aMtzUeRwC0eh8%3D"

# Encode SAS token to avoid special character issues
ENCODED_SAS_TOKEN=$(echo -n "$CONTAINER_SAS_TOKEN" | base64 -w 0)
echo "Encoded SAS ---> $ENCODED_SAS_TOKEN"
DECODED_SAS_TOKEN=$(echo "$ENCODED_SAS_TOKEN" | base64 -d)
echo "Decoded SAS ---> $DECODED_SAS_TOKEN"
