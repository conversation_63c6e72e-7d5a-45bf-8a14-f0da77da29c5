#!/bin/bash
#

# Configuration
RESOURCE_GROUP="volks-loadtest-rg"
STORAGE_ACCOUNT="volksloadtestsa"
CONTAINER_NAME="volks-loadtest-blob"
AGENT_BINARY="agent-linux-amd64"
API_FILE_BASE="apikeys.txt"
ENCODED_SAS_TOKEN=$2
CONTAINER_SAS_TOKEN=$(echo "$ENCODED_SAS_TOKEN" | base64 -d)
ENV=$3

# Set API_FILE
if [ "$ENV" == 'stg' ]; then 
  API_FILE="stg-${API_FILE_BASE}" # "stg-apikeys.txt"
elif [ "$ENV" == 'dev' ]; then
  API_FILE="dev-${API_FILE_BASE}" # "dev-apikeys.txt"
else
  echo "Invalid environment: $ENV"
  exit 1
fi

# Construct the correct URLs with the specific files
AGENT_URL="https://${STORAGE_ACCOUNT}.blob.core.windows.net/${CONTAINER_NAME}/${ENV}/${AGENT_BINARY}?${CONTAINER_SAS_TOKEN}"
APIFILE_URL="https://${STORAGE_ACCOUNT}.blob.core.windows.net/${CONTAINER_NAME}/${ENV}/${API_FILE}?${CONTAINER_SAS_TOKEN}"

get_token(){
  # Ensure that no extra whitespace or trailing endlines
  sed -n "$1p" "$API_FILE" | tr -d '\r\n' | xargs
}

# Stop any existing agent processes
echo "Checking for existing agent processes..."
AGENT_PIDS=$(ps aux | grep '[a]gent-linux-amd64' | awk '{print $2}')

if [ ! -z "$AGENT_PIDS" ]; then
  echo "Found existing agent processes with PIDs: $AGENT_PIDS"
  for PID in $AGENT_PIDS; do
    echo "Stopping process $PID"
    kill "$PID"
    sleep 1
    # Force kill if still running
    if kill -0 "$PID" 2>/dev/null; then
      echo "Force killing process $PID"
      kill -9 "$PID"
    fi
  done
  echo "All existing agent processes stopped"
else
  echo "No existing agent processes found"
fi

# Remove existing files if they exist
if [ -f "$AGENT_BINARY" ]; then
  echo "Removing existing $AGENT_BINARY"
  rm -f "$AGENT_BINARY"
fi

if [ -f "$API_FILE" ]; then
  echo "Removing existing $API_FILE"
  rm -f "$API_FILE"
fi

# Download agent files - amd64 on linux
#
if [ ! -f "$AGENT_BINARY" ]; then
  echo "Downloading $AGENT_BINARY"
  wget "$AGENT_URL" -O "$AGENT_BINARY"
fi

# Download the list of available API keys
if [ ! -f "$API_FILE" ]; then
  echo "Downloading $API_FILE"
  wget "$APIFILE_URL" -O "$API_FILE"
fi

sleep 1

chmod +x agent-linux-amd64
./agent-linux-amd64 --token $(get_token $1) >/dev/null 2>&1 & 
#./"$AGENT_BINARY" --token $(get_token $1) > agent.log 2>&1 &
