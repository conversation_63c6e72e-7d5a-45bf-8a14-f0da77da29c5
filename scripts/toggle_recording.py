#!/usr/bin/env python3
"""
Toggle recording for cameras created by load test setup
- Uses GET /cameras to get all cameras
- Finds cameras created by load test (matching naming pattern)
- Updates recording status to On or Off via PUT /cameras/<camera_id>
"""

"""
Example response from GET /cameras
{
    "code": 0,
    "message": "Success",
    "data": [
        {
            "id": "f4948325-67a5-4774-8f3f-cfbf2eefa7d8",
            "name": "load-test-camera-loadtest-agent-001-001",
            "rtsp": "rtsp://172.16.0.9:8554/stream1",
            "notes": "",
            "retention": "12 months",
            "tags": [],
            "status": "Offline",
            "recording": "On",
            "va_plugins": [],
            "created_at": "2025-07-16T03:30:30.287941832Z",
            "updated_at": "2025-07-16T03:30:30.287941832Z"
        }
    ]
}

Example payload for PUT /cameras/<camera_id>
{
    "name": "load-test-camera-loadtest-agent-001-001",
    "rtsp": "rtsp://172.16.0.9:8554/stream1",
    "notes": "",
    "retention": "12 months",
    "tags": [],
    "recording": "Off",
    "va_plugin_ids": []
}
"""

import requests
import time
import logging
import argparse
from typing import List, Dict, Optional

# Environment-specific configuration
ENVIRONMENTS = {
    'dev': {
        'api_base_url': "https://api.volks-dev.knizsoft.com/api/v1",
        'login_endpoint': "https://auth.volks-dev.knizsoft.com/token?grant_type=password"
    },
    'stg': {
        'api_base_url': "https://api.volks-stg.knizsoft.com/api/v1",
        'login_endpoint': "https://auth.volks-stg.knizsoft.com/token?grant_type=password"
    }
}

API_CAMERA = "/cameras"
API_GET_OPTIONS = "?page-no=1&page-size=100"

# Login Info
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "123456aA@"

# Camera identification
CAMERA_NAME_PREFIX = "load-test-camera-"  # Cameras created by load test start with this

REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 3
DELAY_BETWEEN_REQUESTS = 0.5  # seconds

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('toggle_recording.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class RecordingToggler:
    """Handles toggling recording for load test cameras"""

    def __init__(self, environment: str = 'dev'):
        if environment not in ENVIRONMENTS:
            raise ValueError(f"Invalid environment: {environment}. Must be one of: {list(ENVIRONMENTS.keys())}")

        self.env_config = ENVIRONMENTS[environment]
        self.api_base_url = self.env_config['api_base_url'].rstrip('/')
        self.login_endpoint = self.env_config['login_endpoint']

        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'RecordingToggler/1.0'
        })
        self.bearer_token = None

    def login(self) -> bool:
        """
        Login to get bearer token for authentication

        Returns:
            True if login successful, False otherwise
        """
        payload = {
            "email": ADMIN_EMAIL,
            "password": ADMIN_PASSWORD
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Attempting login (attempt {attempt + 1})")
                response = self.session.post(self.login_endpoint, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                login_data = response.json()

                # Extract access token
                access_token = login_data.get('access_token')
                if not access_token:
                    logger.error("No access token returned from login")
                    return False

                self.bearer_token = access_token

                # Update session headers with bearer token
                self.session.headers.update({
                    'Authorization': f'Bearer {self.bearer_token}'
                })

                # Log token expiry info
                expires_in = login_data.get('expires_in', 'unknown')
                logger.info(f"Login successful! Token expires in {expires_in} seconds")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Login failed (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Login failed after {RETRY_ATTEMPTS} attempts")
        return False

    def get_all_cameras(self) -> List[Dict]:
        """
        Get all cameras from the API

        Returns:
            List of camera dictionaries, or empty list if failed
        """
        url = f"{self.api_base_url}{API_CAMERA}{API_GET_OPTIONS}"

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Fetching all cameras (attempt {attempt + 1})")
                response = self.session.get(url, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                # Check if API returned success
                if response_data.get('code') != 0:
                    logger.error(f"API returned error: {response_data.get('message', 'Unknown error')}")
                    return []

                cameras_data = response_data.get('data', [])
                logger.info(f"Successfully fetched {len(cameras_data)} cameras")
                return cameras_data

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to fetch cameras (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to fetch cameras after {RETRY_ATTEMPTS} attempts")
        return []

    def filter_load_test_cameras(self, cameras: List[Dict]) -> List[Dict]:
        """
        Filter cameras to only include those created by load test

        Args:
            cameras: List of all cameras

        Returns:
            List of load test cameras
        """
        load_test_cameras = []
        for camera in cameras:
            camera_name = camera.get('name', '')
            if camera_name.startswith(CAMERA_NAME_PREFIX):
                load_test_cameras.append(camera)

        logger.info(f"Found {len(load_test_cameras)} load test cameras out of {len(cameras)} total cameras")
        return load_test_cameras

    def update_camera_recording(self, camera: Dict, recording_status: str) -> bool:
        """
        Update camera recording status

        References the set_camera_recording_off function from load-test.py
        Uses the same payload structure for consistency

        Args:
            camera: Camera dictionary from API
            recording_status: "On" or "Off"

        Returns:
            True if successful, False otherwise
        """
        camera_id = camera.get('id')
        camera_name = camera.get('name', 'Unknown')

        if not camera_id:
            logger.error(f"No camera ID found for camera '{camera_name}'")
            return False

        url = f"{self.api_base_url}{API_CAMERA}/{camera_id}"

        # Prepare payload matching the structure from load-test.py set_camera_recording_off function
        payload = {
            "name": camera_name,
            "rtsp": camera.get('rtsp', ''),
            "tags": camera.get('tags', []),
            "notes": camera.get('notes', ''),
            "recording": recording_status,
            "agent_id": camera.get('agent').get('id'),
            "project_id": camera.get('project').get('id')
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Updating camera '{camera_name}' recording to '{recording_status}' (attempt {attempt + 1})")
                response = self.session.put(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                # Check if API returned success
                if response_data.get('code') != 0:
                    logger.error(f"API returned error for camera '{camera_name}': {response_data.get('message', 'Unknown error')}")
                    return False

                logger.info(f"Successfully updated camera '{camera_name}' recording to '{recording_status}'")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to update camera '{camera_name}' (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to update camera '{camera_name}' after {RETRY_ATTEMPTS} attempts")
        return False

    def set_camera_recording_off(self, camera_id: str, camera_name: str, rtsp_url: str, agent_id: str, project_id: str) -> bool:
        """
        Turn off recording for a specific camera via API call

        This function mirrors the set_camera_recording_off function from load-test.py
        for consistency and compatibility

        Args:
            camera_id: ID of the camera to update
            camera_name: Name of the camera (for logging)
            rtsp_url: RTSP URL of the camera
            agent_id: ID of the agent owning the camera
            project_id: ID of the project

        Returns:
            True if recording was turned off successfully, False otherwise
        """
        url = f"{self.api_base_url}{API_CAMERA}/{camera_id}"
        payload = {
            "name": camera_name,
            "rtsp": rtsp_url,
            "tags": [],
            "notes": "",
            "recording": "Off",
            "agent_id": agent_id,
            "project_id": project_id
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Setting camera '{camera_id}' recording off (attempt {attempt + 1})")
                response = self.session.put(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                if response_data.get('code') != 0:
                    logger.error(f"API returned error for camera '{camera_name}': {response_data.get('message', 'Unknown error')}")
                    return False

                logger.info(f"Successfully set camera '{camera_name}' recording off")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to set camera '{camera_id}' recording off (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to set camera '{camera_id}' recording off after {RETRY_ATTEMPTS} attempts")
        return False

    def set_camera_recording_on(self, camera_id: str, camera_name: str, rtsp_url: str, agent_id: str, project_id: str) -> bool:
        """
        Turn on recording for a specific camera via API call

        Companion function to set_camera_recording_off for turning recording back on

        Args:
            camera_id: ID of the camera to update
            camera_name: Name of the camera (for logging)
            rtsp_url: RTSP URL of the camera
            agent_id: ID of the agent owning the camera
            project_id: ID of the project

        Returns:
            True if recording was turned on successfully, False otherwise
        """
        url = f"{self.api_base_url}{API_CAMERA}/{camera_id}"
        payload = {
            "name": camera_name,
            "rtsp": rtsp_url,
            "tags": [],
            "notes": "",
            "recording": "On",
            "agent_id": agent_id,
            "project_id": project_id
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Setting camera '{camera_id}' recording on (attempt {attempt + 1})")
                response = self.session.put(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                if response_data.get('code') != 0:
                    logger.error(f"API returned error for camera '{camera_name}': {response_data.get('message', 'Unknown error')}")
                    return False

                logger.info(f"Successfully set camera '{camera_name}' recording on")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to set camera '{camera_id}' recording on (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to set camera '{camera_id}' recording on after {RETRY_ATTEMPTS} attempts")
        return False

    def toggle_recording(self, recording_status: str) -> bool:
        """
        Toggle recording for all load test cameras

        Args:
            recording_status: "On" or "Off"

        Returns:
            True if all cameras were updated successfully, False otherwise
        """
        logger.info(f"Starting recording toggle to '{recording_status}' for load test cameras")

        # Step 1: Login to get bearer token
        if not self.login():
            logger.error("Failed to login - cannot proceed with recording toggle")
            return False

        # Step 2: Get all cameras
        all_cameras = self.get_all_cameras()
        if not all_cameras:
            logger.error("No cameras found or failed to fetch cameras")
            return False

        # Step 3: Filter to load test cameras
        load_test_cameras = self.filter_load_test_cameras(all_cameras)
        if not load_test_cameras:
            logger.warning("No load test cameras found")
            return True

        # Step 4: Update recording status for each camera
        successful_updates = 0
        failed_updates = []

        for i, camera in enumerate(load_test_cameras, 1):
            camera_name = camera.get('name', f'Camera {i}')
            current_recording = camera.get('recording', 'Unknown')

            logger.info(f"Processing camera {i}/{len(load_test_cameras)}: {camera_name} (current: {current_recording})")

            if self.update_camera_recording(camera, recording_status):
                successful_updates += 1
            else:
                failed_updates.append(camera_name)

            # Small delay between updates to avoid overwhelming the API
            if i < len(load_test_cameras):
                time.sleep(DELAY_BETWEEN_REQUESTS)

        # Summary
        logger.info(f"Recording toggle complete: {successful_updates}/{len(load_test_cameras)} cameras updated successfully")

        if failed_updates:
            logger.warning(f"Failed to update cameras: {failed_updates}")
            return False

        logger.info(f"All load test cameras recording set to '{recording_status}'!")
        return True

    def cleanup(self):
        """Clean up resources"""
        self.session.close()


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description='Toggle recording for load test cameras',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python toggle_recording.py on --env dev     # Turn recording ON for dev environment cameras
  python toggle_recording.py off --env stg    # Turn recording OFF for staging environment cameras
  python toggle_recording.py on               # Turn recording ON for dev environment cameras (default)

This script will:
1. Login to the specified environment API
2. Fetch all cameras
3. Find cameras created by load test (names starting with 'load-test-camera-')
4. Update their recording status to On or Off
        """
    )

    parser.add_argument(
        'action',
        choices=['on', 'off'],
        help='Set recording to On or Off for load test cameras'
    )

    parser.add_argument(
        '--env',
        choices=['dev', 'stg'],
        default='dev',
        help='Environment to use (default: dev)'
    )

    args = parser.parse_args()

    # Convert action to proper case
    recording_status = "On" if args.action.lower() == 'on' else "Off"

    toggler = RecordingToggler(environment=args.env)

    try:
        success = toggler.toggle_recording(recording_status)
        if success:
            logger.info("Recording toggle completed successfully!")
            print(f"\n✅ Recording toggle complete! All load test cameras set to '{recording_status}'")
            print(f"🌍 Environment: {args.env.upper()}")
            print(f"📹 Check the logs for detailed information about each camera update")
        else:
            logger.error("Recording toggle failed!")
            print(f"\n❌ Recording toggle failed! Check the logs for details.")
            print("Some cameras may not have been updated.")
            return 1

    except KeyboardInterrupt:
        logger.info("Recording toggle interrupted by user")
        print("\n⚠️  Recording toggle interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during recording toggle: {e}")
        print(f"\n💥 Unexpected error: {e}")
        return 1
    finally:
        toggler.cleanup()

    return 0


if __name__ == "__main__":
    exit(main())