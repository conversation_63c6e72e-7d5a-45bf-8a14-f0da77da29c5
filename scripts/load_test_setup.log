2025-07-16 11:43:55,802 - INFO - Starting load test setup: 2 agents with 10 cameras each
2025-07-16 11:43:55,802 - INFO - Cleared existing apikeys.txt
2025-07-16 11:43:55,802 - INFO - Setting up agent 1/2
2025-07-16 11:43:55,802 - INFO - Creating agent 'loadtest-agentv2-001' (attempt 1)
2025-07-16 11:43:55,855 - WARNING - Failed to create agent 'loadtest-agentv2-001' (attempt 1): 401 Client Error: Unauthorized for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 11:43:56,856 - INFO - Creating agent 'loadtest-agentv2-001' (attempt 2)
2025-07-16 11:43:56,867 - WARNING - Failed to create agent 'loadtest-agentv2-001' (attempt 2): 401 Client Error: Unauthorized for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 11:43:58,869 - INFO - Creating agent 'loadtest-agentv2-001' (attempt 3)
2025-07-16 11:43:58,877 - WARNING - Failed to create agent 'loadtest-agentv2-001' (attempt 3): 401 Client Error: Unauthorized for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 11:43:58,877 - ERROR - Failed to create agent 'loadtest-agentv2-001' after 3 attempts
2025-07-16 11:43:59,377 - INFO - Setting up agent 2/2
2025-07-16 11:43:59,378 - INFO - Creating agent 'loadtest-agentv2-002' (attempt 1)
2025-07-16 11:43:59,388 - WARNING - Failed to create agent 'loadtest-agentv2-002' (attempt 1): 401 Client Error: Unauthorized for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 11:44:00,389 - INFO - Creating agent 'loadtest-agentv2-002' (attempt 2)
2025-07-16 11:44:00,399 - WARNING - Failed to create agent 'loadtest-agentv2-002' (attempt 2): 401 Client Error: Unauthorized for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 11:44:02,400 - INFO - Creating agent 'loadtest-agentv2-002' (attempt 3)
2025-07-16 11:44:02,410 - WARNING - Failed to create agent 'loadtest-agentv2-002' (attempt 3): 401 Client Error: Unauthorized for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 11:44:02,410 - ERROR - Failed to create agent 'loadtest-agentv2-002' after 3 attempts
2025-07-16 11:44:02,410 - INFO - Setup complete: 0/2 agents created successfully
2025-07-16 11:44:02,410 - WARNING - Failed agents: [1, 2]
2025-07-16 11:44:02,411 - ERROR - Load test setup failed!
2025-07-16 11:53:15,289 - INFO - Starting load test setup: 2 agents with 10 cameras each
2025-07-16 11:53:15,289 - INFO - Attempting login (attempt 1)
2025-07-16 11:53:15,744 - INFO - Login successful! Token expires in 3600 seconds
2025-07-16 11:53:15,744 - INFO - Cleared existing apikeys.txt
2025-07-16 11:53:15,744 - INFO - Setting up agent 1/2
2025-07-16 11:53:15,744 - INFO - Creating agent 'loadtest-agentv2-001' (attempt 1)
2025-07-16 11:53:15,897 - INFO - Successfully created agent 'loadtest-agentv2-001' with ID: fc250d7b-8b3b-4e6b-8c38-e0c26d11480d
2025-07-16 11:53:15,899 - INFO - Saved API key to apikeys.txt
2025-07-16 11:53:15,899 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-001' (attempt 1)
2025-07-16 11:53:15,992 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-001' with ID: 3a62301d-ae49-4341-bf89-5acff0019e32
2025-07-16 11:53:16,493 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-002' (attempt 1)
2025-07-16 11:53:16,579 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-002' with ID: c627b07b-de4f-47f1-af63-614efdd5860f
2025-07-16 11:53:17,079 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-003' (attempt 1)
2025-07-16 11:53:17,153 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-003' with ID: 994c1b9e-d98b-4894-a357-b1410137f0a3
2025-07-16 11:53:17,654 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-004' (attempt 1)
2025-07-16 11:53:17,725 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-004' with ID: 2ebdf68c-5acb-43cb-af05-7b814c44fdd0
2025-07-16 11:53:18,225 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-005' (attempt 1)
2025-07-16 11:53:18,300 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-005' with ID: 57e61bca-0ee8-4321-b674-08b67b0d1a59
2025-07-16 11:53:18,801 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-006' (attempt 1)
2025-07-16 11:53:18,870 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-006' with ID: 649e04bf-438e-45d8-b3b6-57931a48081a
2025-07-16 11:53:19,370 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-007' (attempt 1)
2025-07-16 11:53:19,443 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-007' with ID: 943b4e25-d50e-43fa-b4c4-a98ced4e2d76
2025-07-16 11:53:19,944 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-008' (attempt 1)
2025-07-16 11:53:20,016 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-008' with ID: 3cd33443-690a-45a3-a1c4-95bdbec7df9b
2025-07-16 11:53:20,517 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-009' (attempt 1)
2025-07-16 11:53:20,765 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-009' with ID: fecd51b7-29a3-45e8-b491-88c79d1080c7
2025-07-16 11:53:21,265 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-001-010' (attempt 1)
2025-07-16 11:53:21,344 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-001-010' with ID: 40841f96-e780-41e5-a39c-35e5407a3960
2025-07-16 11:53:21,844 - INFO - Successfully setup agent 'loadtest-agentv2-001' with 10 cameras
2025-07-16 11:53:22,345 - INFO - Setting up agent 2/2
2025-07-16 11:53:22,346 - INFO - Creating agent 'loadtest-agentv2-002' (attempt 1)
2025-07-16 11:53:22,423 - INFO - Successfully created agent 'loadtest-agentv2-002' with ID: 8af8f680-81c9-440d-9bdc-077598bfea47
2025-07-16 11:53:22,425 - INFO - Saved API key to apikeys.txt
2025-07-16 11:53:22,425 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-001' (attempt 1)
2025-07-16 11:53:22,497 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-001' with ID: 0417ae64-77ff-4841-aae0-d2f094e9d7c3
2025-07-16 11:53:22,998 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-002' (attempt 1)
2025-07-16 11:53:23,082 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-002' with ID: 68a57b1c-cbd2-4382-9ab7-c9773001b8b6
2025-07-16 11:53:23,582 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-003' (attempt 1)
2025-07-16 11:53:23,647 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-003' with ID: 681813ed-61b0-4fc8-9b57-9818ca004711
2025-07-16 11:53:24,147 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-004' (attempt 1)
2025-07-16 11:53:24,214 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-004' with ID: a2d7c3f0-51c8-4798-b34d-337021f89d5f
2025-07-16 11:53:24,715 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-005' (attempt 1)
2025-07-16 11:53:24,783 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-005' with ID: 609ebd38-8272-48ab-9613-55063f23b9a1
2025-07-16 11:53:25,284 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-006' (attempt 1)
2025-07-16 11:53:25,355 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-006' with ID: 3f12cab5-224a-40db-acca-98185d3f0e69
2025-07-16 11:53:25,856 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-007' (attempt 1)
2025-07-16 11:53:25,934 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-007' with ID: 93eed13d-5b91-44ab-a9ca-c67201b24da9
2025-07-16 11:53:26,434 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-008' (attempt 1)
2025-07-16 11:53:26,514 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-008' with ID: d3bb639d-c6ff-49eb-a866-ddbd9740deef
2025-07-16 11:53:27,014 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-009' (attempt 1)
2025-07-16 11:53:27,080 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-009' with ID: f6dd44c1-eca5-4231-8a5e-bf85604f7538
2025-07-16 11:53:27,580 - INFO - Creating camera 'load-test-camera-loadtest-agentv2-002-010' (attempt 1)
2025-07-16 11:53:27,649 - INFO - Successfully created camera 'load-test-camera-loadtest-agentv2-002-010' with ID: 84191078-8a16-4275-875a-74adfcb273cf
2025-07-16 11:53:28,150 - INFO - Successfully setup agent 'loadtest-agentv2-002' with 10 cameras
2025-07-16 11:53:28,151 - INFO - Setup complete: 2/2 agents created successfully
2025-07-16 11:53:28,151 - INFO - All API keys saved to apikeys.txt
2025-07-16 11:53:28,151 - INFO - Load test setup completed successfully!
2025-07-16 15:39:15,823 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-16 15:39:15,824 - INFO - Attempting login (attempt 1)
2025-07-16 15:39:16,074 - INFO - Login successful! Token expires in 3600 seconds
2025-07-16 15:39:16,075 - INFO - Cleared existing apikeys.txt
2025-07-16 15:39:16,075 - INFO - Setting up agent 1/5
2025-07-16 15:39:16,075 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-16 15:39:16,203 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 535aaac1-f04f-4987-b32c-629c5d733ade
2025-07-16 15:39:16,205 - INFO - Saved API key to apikeys.txt
2025-07-16 15:39:16,205 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-16 15:39:16,279 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 957881c1-6894-46a8-93b2-3772d02f98d4
2025-07-16 15:39:16,780 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-16 15:39:16,868 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 8320d9d9-4120-4c15-aab0-c24a24730e42
2025-07-16 15:39:17,369 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-16 15:39:17,441 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 1825ecb6-fdec-4c2e-bc28-3e6b1d969219
2025-07-16 15:39:17,942 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-16 15:39:18,020 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: b46f965e-f88f-417b-b85a-6d2b5f4fc2d8
2025-07-16 15:39:18,522 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-16 15:39:18,594 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: e9bcde22-54a1-470d-88d9-ccda14cb95aa
2025-07-16 15:39:19,095 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-16 15:39:19,595 - INFO - Setting up agent 2/5
2025-07-16 15:39:19,596 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-16 15:39:19,658 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 73ad29f0-3872-44ed-b689-9d2361fb6d9e
2025-07-16 15:39:19,659 - INFO - Saved API key to apikeys.txt
2025-07-16 15:39:19,659 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-16 15:39:19,729 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 2595cb51-e1d6-43d4-b98d-5f03651f65dd
2025-07-16 15:39:20,229 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-16 15:39:20,300 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: b97d62a5-a034-4f81-88df-e8caecc76b62
2025-07-16 15:39:20,802 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-16 15:39:20,875 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: b8f51429-7b1f-4501-8c81-89e1e5e0642c
2025-07-16 15:39:21,375 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-16 15:39:21,458 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: 2e9534af-ba26-4853-8f0e-86ae9ed0b199
2025-07-16 15:39:21,959 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-16 15:39:22,038 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 818be2bc-4b5b-44fb-bd76-a45a377f1d7f
2025-07-16 15:39:22,538 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-16 15:39:23,039 - INFO - Setting up agent 3/5
2025-07-16 15:39:23,040 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-16 15:39:23,105 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 8573dc8e-58fb-4006-af1b-37e92edfe03f
2025-07-16 15:39:23,106 - INFO - Saved API key to apikeys.txt
2025-07-16 15:39:23,106 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-16 15:39:23,179 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: 466492f1-7a26-440f-b03c-15f30efe8de7
2025-07-16 15:39:23,680 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-16 15:39:23,756 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: be9c85ef-2490-4beb-97b1-8805d15ae5de
2025-07-16 15:39:24,257 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-16 15:39:24,326 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: 408a0c38-339d-49ec-b20d-040d01f3383d
2025-07-16 15:39:24,827 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-16 15:39:24,891 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 92e73e6d-4a49-497a-8ed2-ca1e3ad35a9c
2025-07-16 15:39:25,393 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-16 15:39:25,471 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 8aad14dd-9f12-4d86-9161-6809b14dd375
2025-07-16 15:39:25,972 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-16 15:39:26,473 - INFO - Setting up agent 4/5
2025-07-16 15:39:26,473 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-16 15:39:26,539 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 1412e75d-8f26-41f9-a3c2-9753f975eacc
2025-07-16 15:39:26,540 - INFO - Saved API key to apikeys.txt
2025-07-16 15:39:26,540 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-16 15:39:26,606 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: facb89c9-2f29-4496-9681-4542b8056403
2025-07-16 15:39:27,106 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-16 15:39:27,175 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 7830f66a-7100-47b3-b396-1e5289afb34a
2025-07-16 15:39:27,676 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-16 15:39:27,744 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 7f887018-eaf2-4add-abd9-89ba07452a1e
2025-07-16 15:39:28,244 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-16 15:39:28,305 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: b4631238-17bd-4180-9f34-8ab28bf6647b
2025-07-16 15:39:28,806 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-16 15:39:28,871 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 32bd6e68-ea32-414b-99aa-abe855bcae80
2025-07-16 15:39:29,371 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-16 15:39:29,872 - INFO - Setting up agent 5/5
2025-07-16 15:39:29,873 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-16 15:39:29,944 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 5b32f15d-afbb-4fa5-905e-a976744d23b8
2025-07-16 15:39:29,944 - INFO - Saved API key to apikeys.txt
2025-07-16 15:39:29,944 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-16 15:39:30,015 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 8492b45b-8fd6-4458-b4fe-383915906394
2025-07-16 15:39:30,516 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-16 15:39:30,595 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: 65b901f1-5ec7-4e20-ac37-dfa25c573bf8
2025-07-16 15:39:31,095 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-16 15:39:31,167 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 53c66ac3-2316-4a4e-8366-06c302084c17
2025-07-16 15:39:31,667 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-16 15:39:31,738 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: 4e594c3a-497c-4db0-8855-53c1ebc5c355
2025-07-16 15:39:32,238 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-16 15:39:32,307 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: a0671a06-b5f0-4bd8-aa72-969b84edd012
2025-07-16 15:39:32,808 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-16 15:39:32,809 - INFO - Setup complete: 5/5 agents created successfully
2025-07-16 15:39:32,809 - INFO - All API keys saved to apikeys.txt
2025-07-16 15:39:32,809 - INFO - Load test setup completed successfully!
2025-07-16 16:44:07,310 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-16 16:44:07,310 - INFO - Attempting login (attempt 1)
2025-07-16 16:44:07,539 - INFO - Login successful! Token expires in 3600 seconds
2025-07-16 16:44:07,539 - INFO - Cleared existing apikeys.txt
2025-07-16 16:44:07,539 - INFO - Cleared existing agent_ids.txt
2025-07-16 16:44:07,539 - INFO - Setting up agent 1/5
2025-07-16 16:44:07,539 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-16 16:44:07,668 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 544bebb3-6a86-4652-b2a6-780f84b8811b
2025-07-16 16:44:07,670 - INFO - Saved API key to apikeys.txt
2025-07-16 16:44:07,672 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:44:07,673 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-16 16:44:07,834 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: ec76208b-4dde-4803-a505-c933015ead95
2025-07-16 16:44:08,335 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-16 16:44:08,424 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: fa68d944-332a-43d9-a23b-5f12f085119e
2025-07-16 16:44:08,925 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-16 16:44:08,994 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: dabbf49a-cac6-4136-8b01-a0a54dec7849
2025-07-16 16:44:09,495 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-16 16:44:09,562 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 795b7742-e42f-4814-a073-a29fb39293f3
2025-07-16 16:44:10,063 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-16 16:44:10,129 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 99936bd7-19ec-4641-b7d2-b5113a273a5f
2025-07-16 16:44:10,629 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-16 16:44:11,130 - INFO - Setting up agent 2/5
2025-07-16 16:44:11,130 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-16 16:44:11,200 - INFO - Successfully created agent 'loadtest-agent-002' with ID: cf493959-4cc5-4309-bf12-02802eeab1f5
2025-07-16 16:44:11,201 - INFO - Saved API key to apikeys.txt
2025-07-16 16:44:11,202 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:44:11,202 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-16 16:44:11,290 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: edbd32a5-0796-4caa-b596-84cf3a953b5d
2025-07-16 16:44:11,790 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-16 16:44:11,857 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 13bf47c1-2318-4de2-97d3-982b0c952f00
2025-07-16 16:44:12,358 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-16 16:44:12,418 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: e2ac387c-92d5-41e6-b38f-77996eb8e7c0
2025-07-16 16:44:12,919 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-16 16:44:12,987 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: 0aac2f55-fb21-4197-a793-f0385278a8d6
2025-07-16 16:44:13,487 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-16 16:44:13,555 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: bd88f0d7-940a-49e4-a3cc-c6dce03eb171
2025-07-16 16:44:14,055 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-16 16:44:14,556 - INFO - Setting up agent 3/5
2025-07-16 16:44:14,556 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-16 16:44:14,620 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 40aeba78-5928-4849-85af-6b518a7ac025
2025-07-16 16:44:14,620 - INFO - Saved API key to apikeys.txt
2025-07-16 16:44:14,620 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:44:14,620 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-16 16:44:14,688 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: c1f84250-7d10-44d6-9e3e-6ed6f1144328
2025-07-16 16:44:15,188 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-16 16:44:15,250 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: 46e62460-8ae5-4360-96f1-82d75c30fa28
2025-07-16 16:44:15,750 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-16 16:44:15,815 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: 78386f64-844e-4a11-93c2-04309ed0975b
2025-07-16 16:44:16,315 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-16 16:44:16,376 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 7d024c5c-dd23-4b9a-a3ba-811caad437c8
2025-07-16 16:44:16,877 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-16 16:44:16,963 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 100fb7a8-d186-4bcd-a6b8-edd6723575d4
2025-07-16 16:44:17,464 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-16 16:44:17,965 - INFO - Setting up agent 4/5
2025-07-16 16:44:17,966 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-16 16:44:18,031 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 5231dbdf-b782-4c9b-b1cf-53b845cdaa89
2025-07-16 16:44:18,032 - INFO - Saved API key to apikeys.txt
2025-07-16 16:44:18,032 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:44:18,032 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-16 16:44:18,094 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 6d1c4b32-dec8-470c-aa60-65733f1ebde8
2025-07-16 16:44:18,594 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-16 16:44:18,663 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 5bff2c76-a41e-466d-86aa-65468a82d3cd
2025-07-16 16:44:19,163 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-16 16:44:19,232 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 53e3feeb-5564-4cc3-b6a4-9b26960b4169
2025-07-16 16:44:19,733 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-16 16:44:19,799 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: e3df6cb4-5af9-4ef3-9639-57adeac0c51d
2025-07-16 16:44:20,300 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-16 16:44:20,374 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 17d5a171-d45e-4b86-b449-f6a987b48911
2025-07-16 16:44:20,874 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-16 16:44:21,375 - INFO - Setting up agent 5/5
2025-07-16 16:44:21,375 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-16 16:44:21,439 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 082eb452-fffd-4d42-aecc-3c22ba7e11d4
2025-07-16 16:44:21,440 - INFO - Saved API key to apikeys.txt
2025-07-16 16:44:21,441 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:44:21,441 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-16 16:44:21,502 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: e4a015be-c61f-4fb4-8845-1a6b7e7430e0
2025-07-16 16:44:22,002 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-16 16:44:22,070 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: c59ec302-0228-455f-9dcc-9b5bd56eb5d4
2025-07-16 16:44:22,570 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-16 16:44:22,642 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 3732ea20-9d15-4827-8f06-d18c6acc258c
2025-07-16 16:44:23,142 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-16 16:44:23,208 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: e5c8cc5e-2687-4c0d-ba8a-9f9ea9c90b81
2025-07-16 16:44:23,708 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-16 16:44:23,778 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 90ce6c4e-0560-48af-a686-033bac9c1044
2025-07-16 16:44:24,279 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-16 16:44:24,279 - INFO - Setup complete: 5/5 agents created successfully
2025-07-16 16:44:24,279 - INFO - All API keys saved to apikeys.txt
2025-07-16 16:44:24,279 - INFO - All agent IDs saved to agent_ids.txt
2025-07-16 16:44:24,279 - INFO - Load test setup completed successfully!
2025-07-16 16:46:19,869 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-16 16:46:19,869 - INFO - Attempting login (attempt 1)
2025-07-16 16:46:20,140 - INFO - Login successful! Token expires in 3600 seconds
2025-07-16 16:46:20,140 - INFO - Cleared existing apikeys.txt
2025-07-16 16:46:20,141 - INFO - Cleared existing agent_ids.txt
2025-07-16 16:46:20,141 - INFO - Setting up agent 1/5
2025-07-16 16:46:20,141 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-16 16:46:20,245 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 1ee15a67-62a9-4236-a1a8-94de83e947c8
2025-07-16 16:46:20,246 - INFO - Saved API key to apikeys.txt
2025-07-16 16:46:20,246 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:46:20,246 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-16 16:46:20,316 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 8d57873c-1359-4c28-a444-b576875f949c
2025-07-16 16:46:20,816 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-16 16:46:20,898 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: fc7cfc5a-e771-4188-a809-921d9ddf3bd4
2025-07-16 16:46:21,398 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-16 16:46:21,476 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: aea45903-0721-4f3c-9fb7-72daff99a4b8
2025-07-16 16:46:21,976 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-16 16:46:22,054 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 7ea51626-a022-4aac-b2c6-d4354fba3bcc
2025-07-16 16:46:22,555 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-16 16:46:22,633 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 522b8370-0488-40e0-9b11-2d85af96c82f
2025-07-16 16:46:23,134 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-16 16:46:23,635 - INFO - Setting up agent 2/5
2025-07-16 16:46:23,635 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-16 16:46:23,705 - INFO - Successfully created agent 'loadtest-agent-002' with ID: fb712714-f309-4f44-b97e-5a180a55e40f
2025-07-16 16:46:23,705 - INFO - Saved API key to apikeys.txt
2025-07-16 16:46:23,705 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:46:23,706 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-16 16:46:23,769 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 84c23b0c-15ae-469f-bd0a-204af25e69a0
2025-07-16 16:46:24,269 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-16 16:46:24,335 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: c29cdb63-2c32-46f3-9314-8421cda2eada
2025-07-16 16:46:24,836 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-16 16:46:25,010 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: ba56342a-20d9-42f8-a55c-0d9af0496a4c
2025-07-16 16:46:25,511 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-16 16:46:25,584 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: f3f39e7f-22fb-4e94-a81e-d5803b6c9147
2025-07-16 16:46:26,085 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-16 16:46:26,163 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 1f9b3ea9-d3d4-4a9c-9216-eef9182dd7de
2025-07-16 16:46:26,663 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-16 16:46:27,164 - INFO - Setting up agent 3/5
2025-07-16 16:46:27,164 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-16 16:46:27,234 - INFO - Successfully created agent 'loadtest-agent-003' with ID: ece20f23-6356-43e6-bada-f19d349163d6
2025-07-16 16:46:27,235 - INFO - Saved API key to apikeys.txt
2025-07-16 16:46:27,235 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:46:27,235 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-16 16:46:27,309 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: b72b00f4-4ba0-460a-b094-96aac8560775
2025-07-16 16:46:27,809 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-16 16:46:27,887 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: 132e3afa-5b56-4b71-a240-bda2e491faa9
2025-07-16 16:46:28,387 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-16 16:46:28,467 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: be4afe7b-e819-4111-b0d5-d89765034a93
2025-07-16 16:46:28,968 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-16 16:46:29,035 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 09463629-fa53-415a-9f27-9d1599db8614
2025-07-16 16:46:29,535 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-16 16:46:29,601 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 93503c05-ebd0-477c-89a2-1e082f3588ed
2025-07-16 16:46:30,102 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-16 16:46:30,603 - INFO - Setting up agent 4/5
2025-07-16 16:46:30,604 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-16 16:46:30,666 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 44b6d2a4-db18-41de-8448-246e8b268fe7
2025-07-16 16:46:30,667 - INFO - Saved API key to apikeys.txt
2025-07-16 16:46:30,667 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:46:30,667 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-16 16:46:30,738 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 97f5ff19-6da3-4909-8cec-4a698ce1604a
2025-07-16 16:46:31,238 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-16 16:46:31,302 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 082531c2-d84f-4e4e-988f-cf14587ecfd9
2025-07-16 16:46:31,803 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-16 16:46:31,865 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 818a3574-782b-4420-b336-0b945d528ea2
2025-07-16 16:46:32,366 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-16 16:46:32,444 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: f2dd2f08-a69e-485a-9fc9-cf09472b036e
2025-07-16 16:46:32,945 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-16 16:46:33,010 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: a4d43547-4393-433c-96a1-1e1b26844bd3
2025-07-16 16:46:33,510 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-16 16:46:34,011 - INFO - Setting up agent 5/5
2025-07-16 16:46:34,011 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-16 16:46:34,068 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 03420b69-3594-48bf-8df4-62500e963a48
2025-07-16 16:46:34,069 - INFO - Saved API key to apikeys.txt
2025-07-16 16:46:34,069 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 16:46:34,069 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-16 16:46:34,190 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 0f7800a0-255b-4267-8973-8b0cdcc34d1d
2025-07-16 16:46:34,691 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-16 16:46:34,757 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: 17734953-526f-4f23-afba-82b29b877cac
2025-07-16 16:46:35,257 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-16 16:46:35,318 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 15fe7dc0-a3d1-4be9-8f51-29f0f0a73f6b
2025-07-16 16:46:35,819 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-16 16:46:35,899 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: 516931de-26c0-492a-b244-cacefde9dbba
2025-07-16 16:46:36,399 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-16 16:46:36,472 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 078c9096-0413-4443-8cdf-8be3e89d0cff
2025-07-16 16:46:36,972 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-16 16:46:36,973 - INFO - Setup complete: 5/5 agents created successfully
2025-07-16 16:46:36,973 - INFO - All API keys saved to apikeys.txt
2025-07-16 16:46:36,973 - INFO - All agent IDs saved to agent_ids.txt
2025-07-16 16:46:36,973 - INFO - Load test setup completed successfully!
2025-07-16 17:59:25,023 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-16 17:59:25,023 - INFO - Attempting login (attempt 1)
2025-07-16 17:59:25,285 - INFO - Login successful! Token expires in 3600 seconds
2025-07-16 17:59:25,285 - INFO - Cleared existing apikeys.txt
2025-07-16 17:59:25,285 - INFO - Cleared existing agent_ids.txt
2025-07-16 17:59:25,285 - INFO - Setting up agent 1/5
2025-07-16 17:59:25,285 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-16 17:59:25,617 - INFO - Successfully created agent 'loadtest-agent-001' with ID: ebceabfe-52aa-4aaf-a621-455d86042de6
2025-07-16 17:59:25,619 - INFO - Saved API key to apikeys.txt
2025-07-16 17:59:25,620 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 17:59:25,620 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-16 17:59:25,695 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 5a68db78-b328-434a-bf26-18a6b7864c9b
2025-07-16 17:59:26,196 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-16 17:59:26,315 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 41ff9a61-7968-4f2b-b387-3962f15e85d3
2025-07-16 17:59:26,815 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-16 17:59:26,901 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 1bf54032-14bb-41f0-ba93-9cc98176e74a
2025-07-16 17:59:27,402 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-16 17:59:27,531 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 042e08a1-6615-4d1c-b695-75e5b5eccceb
2025-07-16 17:59:28,032 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-16 17:59:28,134 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 00b095a3-a771-4f10-9672-0414f5f72f5c
2025-07-16 17:59:28,634 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-16 17:59:29,136 - INFO - Setting up agent 2/5
2025-07-16 17:59:29,136 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-16 17:59:29,209 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 2f42011a-4f49-4073-af7a-f55cfc0ffe04
2025-07-16 17:59:29,212 - INFO - Saved API key to apikeys.txt
2025-07-16 17:59:29,215 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 17:59:29,215 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-16 17:59:29,288 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 027e594c-a35a-4d97-8566-b0dcc30cbea5
2025-07-16 17:59:29,789 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-16 17:59:29,908 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 2f12cc9a-fb5a-423a-bd4b-89ffc7b6d0a8
2025-07-16 17:59:30,409 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-16 17:59:30,539 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: 5df9f7ad-0fe3-4997-9a70-c626850fcef6
2025-07-16 17:59:31,040 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-16 17:59:31,216 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: b95e9714-205c-462e-a6f2-1b41283d4d5c
2025-07-16 17:59:31,717 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-16 17:59:31,799 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 30ff21f4-fbf3-4a5d-8873-329be8c01c0f
2025-07-16 17:59:32,300 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-16 17:59:32,802 - INFO - Setting up agent 3/5
2025-07-16 17:59:32,802 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-16 17:59:32,877 - INFO - Successfully created agent 'loadtest-agent-003' with ID: bdfa3d35-4b37-431c-a864-4413798db34f
2025-07-16 17:59:32,879 - INFO - Saved API key to apikeys.txt
2025-07-16 17:59:32,881 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 17:59:32,882 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-16 17:59:32,957 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: 1314664c-c847-4845-8a38-2da0dd09dda1
2025-07-16 17:59:33,458 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-16 17:59:33,540 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: b070b781-3cd6-4183-ac82-9001319b1ffe
2025-07-16 17:59:34,041 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-16 17:59:34,123 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: a57499b9-7b27-40c8-b550-6269a0ecd296
2025-07-16 17:59:34,625 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-16 17:59:34,701 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 522028f7-15b7-4b61-81cb-5e73554ae605
2025-07-16 17:59:35,202 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-16 17:59:35,289 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 3c12e361-c1c6-4b42-b2ce-7f6dc36d88c1
2025-07-16 17:59:35,790 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-16 17:59:36,291 - INFO - Setting up agent 4/5
2025-07-16 17:59:36,292 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-16 17:59:36,375 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 1d9b3e8a-f3c6-4e51-a730-c4a9053f3fc9
2025-07-16 17:59:36,376 - INFO - Saved API key to apikeys.txt
2025-07-16 17:59:36,378 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 17:59:36,379 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-16 17:59:36,478 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 391cdd71-9f1d-4cf6-9fe5-ce0acec7efd7
2025-07-16 17:59:36,979 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-16 17:59:37,093 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 75829c10-4385-4d80-865c-3db43de7eb56
2025-07-16 17:59:37,593 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-16 17:59:37,700 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 54d670b6-f302-43de-b503-7c3d344c6fbf
2025-07-16 17:59:38,200 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-16 17:59:38,263 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: 2012986d-f0e0-46ed-93fa-f60fa8b96fe4
2025-07-16 17:59:38,764 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-16 17:59:38,879 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 73ed1483-8a69-4dfe-beda-310e569806b5
2025-07-16 17:59:39,380 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-16 17:59:39,881 - INFO - Setting up agent 5/5
2025-07-16 17:59:39,882 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-16 17:59:39,968 - INFO - Successfully created agent 'loadtest-agent-005' with ID: fc7b4d48-25a6-4d9b-92c8-d42c0ea893ce
2025-07-16 17:59:39,970 - INFO - Saved API key to apikeys.txt
2025-07-16 17:59:39,972 - INFO - Saved agent ID to agent_ids.txt
2025-07-16 17:59:39,972 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-16 17:59:40,081 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: b13dc4f9-b85a-4805-b440-7fb4ad9e6da0
2025-07-16 17:59:40,581 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-16 17:59:40,674 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: 6248d343-3084-4941-91e7-60519d54e7e3
2025-07-16 17:59:41,175 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-16 17:59:41,253 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 755ea8cd-d400-470f-82e6-12b10d5abc10
2025-07-16 17:59:41,753 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-16 17:59:41,834 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: 3e0e4654-5e19-4271-b87e-5266532ce6b4
2025-07-16 17:59:42,334 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-16 17:59:42,424 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 87e4be6b-01ce-4658-a01e-46bb90544fb4
2025-07-16 17:59:42,925 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-16 17:59:42,925 - INFO - Setup complete: 5/5 agents created successfully
2025-07-16 17:59:42,925 - INFO - All API keys saved to apikeys.txt
2025-07-16 17:59:42,926 - INFO - All agent IDs saved to agent_ids.txt
2025-07-16 17:59:42,926 - INFO - Load test setup completed successfully!
2025-07-16 18:11:32,868 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-16 18:11:32,868 - INFO - Attempting login (attempt 1)
2025-07-16 18:11:33,343 - INFO - Login successful! Token expires in 3600 seconds
2025-07-16 18:11:33,344 - INFO - Cleared existing apikeys.txt
2025-07-16 18:11:33,344 - INFO - Cleared existing agent_ids.txt
2025-07-16 18:11:33,344 - INFO - Setting up agent 1/5
2025-07-16 18:11:33,345 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-16 18:11:33,424 - WARNING - Failed to create agent 'loadtest-agent-001' (attempt 1): 500 Server Error: Internal Server Error for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 18:11:34,424 - INFO - Creating agent 'loadtest-agent-001' (attempt 2)
2025-07-16 18:11:34,472 - WARNING - Failed to create agent 'loadtest-agent-001' (attempt 2): 500 Server Error: Internal Server Error for url: https://api.volks-stg.knizsoft.com/api/v1/agents
2025-07-16 18:11:34,717 - INFO - Setup interrupted by user
2025-07-18 09:30:59,175 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-18 09:30:59,175 - INFO - Attempting login (attempt 1)
2025-07-18 09:30:59,473 - INFO - Login successful! Token expires in 3600 seconds
2025-07-18 09:30:59,473 - INFO - Cleared existing apikeys.txt
2025-07-18 09:30:59,473 - INFO - Cleared existing agent_ids.txt
2025-07-18 09:30:59,473 - INFO - Setting up agent 1/5
2025-07-18 09:30:59,474 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-18 09:30:59,616 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 19a145be-6e1b-4988-813c-1c5a5b7585c0
2025-07-18 09:30:59,618 - INFO - Saved API key to apikeys.txt
2025-07-18 09:30:59,619 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:30:59,619 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-18 09:30:59,710 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 5ba30578-a19e-46d9-b84a-f124b4b8e369
2025-07-18 09:31:00,211 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-18 09:31:00,315 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 25ccc1f1-b4b3-4a27-bca1-2266feb34b67
2025-07-18 09:31:00,816 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-18 09:31:00,906 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 72fb04dd-4f0b-46fd-880c-45981e39acfe
2025-07-18 09:31:01,407 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-18 09:31:01,486 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: e0c99f4f-2203-4b09-9363-9361c72a47fc
2025-07-18 09:31:01,987 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-18 09:31:02,056 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 141c410d-ee53-4c31-93d3-e1bcf27c1753
2025-07-18 09:31:02,557 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-18 09:31:03,058 - INFO - Setting up agent 2/5
2025-07-18 09:31:03,058 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-18 09:31:03,122 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 80fe7cb9-03f1-4f5e-996a-8a46994c4080
2025-07-18 09:31:03,123 - INFO - Saved API key to apikeys.txt
2025-07-18 09:31:03,124 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:31:03,124 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-18 09:31:03,196 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 4486a8c0-023e-45a1-aa44-6f3e1b94c824
2025-07-18 09:31:03,697 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-18 09:31:03,789 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: c4f5ece5-c870-4071-b66e-a8b878ab18b0
2025-07-18 09:31:04,290 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-18 09:31:04,368 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: 6a368aad-d171-4007-bac9-faae2d175cc9
2025-07-18 09:31:04,869 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-18 09:31:05,127 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: ef5c3844-494b-4d78-a88c-bb756f308fb4
2025-07-18 09:31:05,628 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-18 09:31:05,698 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 1778d6e6-03ba-4471-a1ed-2873e5b69df8
2025-07-18 09:31:06,199 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-18 09:31:06,699 - INFO - Setting up agent 3/5
2025-07-18 09:31:06,699 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-18 09:31:06,767 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 45cbb8ee-ffa1-4f9f-9477-c3a85c835842
2025-07-18 09:31:06,768 - INFO - Saved API key to apikeys.txt
2025-07-18 09:31:06,769 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:31:06,769 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-18 09:31:06,834 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: 724a97db-8e5b-492d-b289-5d5b239f9707
2025-07-18 09:31:07,335 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-18 09:31:07,408 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: 6f0438c4-523b-419c-ab12-cde4431ea0ad
2025-07-18 09:31:07,909 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-18 09:31:07,979 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: a8372111-a4ab-418d-a107-7c240266ca9b
2025-07-18 09:31:08,480 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-18 09:31:08,551 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 2641274b-b1aa-422e-ab41-86d1e2c85c8d
2025-07-18 09:31:09,052 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-18 09:31:09,129 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: cfd931b6-0397-40b7-b877-2b0c6de9f19b
2025-07-18 09:31:09,629 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-18 09:31:10,130 - INFO - Setting up agent 4/5
2025-07-18 09:31:10,130 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-18 09:31:10,191 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 237fd1f3-f7f2-4c12-a9fa-70c7dd608e51
2025-07-18 09:31:10,191 - INFO - Saved API key to apikeys.txt
2025-07-18 09:31:10,191 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:31:10,191 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-18 09:31:10,254 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 87dfb641-c685-4b51-989a-9013e9533b18
2025-07-18 09:31:10,755 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-18 09:31:10,824 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: d0fa80df-e0f2-4585-80b2-f3ada73ac228
2025-07-18 09:31:11,325 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-18 09:31:11,402 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 66afd078-89f0-4db5-acce-2d3559406012
2025-07-18 09:31:11,903 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-18 09:31:11,980 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: 15d1059c-a97c-4526-aca7-a3802b8e038b
2025-07-18 09:31:12,481 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-18 09:31:12,546 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 6a1d84f4-9977-49de-a699-fd7664d1d222
2025-07-18 09:31:13,047 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-18 09:31:13,548 - INFO - Setting up agent 5/5
2025-07-18 09:31:13,548 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-18 09:31:13,604 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 19724931-476a-48e4-9402-28f2e3d1d76f
2025-07-18 09:31:13,604 - INFO - Saved API key to apikeys.txt
2025-07-18 09:31:13,605 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:31:13,605 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-18 09:31:13,675 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: cab6dd3a-1d7e-416a-867a-45dd5e16a4ab
2025-07-18 09:31:14,175 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-18 09:31:14,248 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: 0d344d9b-2f30-45b4-b827-29bec82beab3
2025-07-18 09:31:14,749 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-18 09:31:14,822 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 1451d186-00ff-429e-970c-00435a5a3f29
2025-07-18 09:31:15,322 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-18 09:31:15,387 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: 4261131d-4ad0-403b-b6ce-7310134a05ff
2025-07-18 09:31:15,889 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-18 09:31:15,953 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 35623114-da7a-4e1e-9f67-538ac1abf30f
2025-07-18 09:31:16,454 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-18 09:31:16,454 - INFO - Setup complete: 5/5 agents created successfully
2025-07-18 09:31:16,455 - INFO - All API keys saved to apikeys.txt
2025-07-18 09:31:16,455 - INFO - All agent IDs saved to agent_ids.txt
2025-07-18 09:31:16,455 - INFO - Load test setup completed successfully!
2025-07-18 09:45:11,861 - INFO - Starting load test setup: 5 agents with 5 cameras each
2025-07-18 09:45:11,861 - INFO - Attempting login (attempt 1)
2025-07-18 09:45:12,042 - INFO - Login successful! Token expires in 3600 seconds
2025-07-18 09:45:12,042 - INFO - Cleared existing apikeys.txt
2025-07-18 09:45:12,042 - INFO - Cleared existing agent_ids.txt
2025-07-18 09:45:12,042 - INFO - Setting up agent 1/5
2025-07-18 09:45:12,042 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-18 09:45:12,176 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 21ff26ca-6d97-4690-89ee-1aab13829f80
2025-07-18 09:45:12,177 - INFO - Saved API key to apikeys.txt
2025-07-18 09:45:12,178 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:45:12,178 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-18 09:45:12,250 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 5f5f0943-15d6-4f44-af8b-fa5a0b5ef120
2025-07-18 09:45:12,250 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream01
2025-07-18 09:45:12,751 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-18 09:45:12,819 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: ed0bc714-260d-424f-9803-cf4e059c633b
2025-07-18 09:45:12,819 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream02
2025-07-18 09:45:13,320 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-18 09:45:13,384 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 4febba4d-0aa7-4c78-b6cc-394e6c1934b4
2025-07-18 09:45:13,384 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream03
2025-07-18 09:45:13,884 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-18 09:45:14,259 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 20e42796-af00-4262-bc91-2db89a892283
2025-07-18 09:45:14,259 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream04
2025-07-18 09:45:14,760 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-18 09:45:14,843 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: e45dbd4e-f03a-4cf9-b4de-2c3745767c2c
2025-07-18 09:45:14,843 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream05
2025-07-18 09:45:15,343 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-18 09:45:15,844 - INFO - Setting up agent 2/5
2025-07-18 09:45:15,844 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-18 09:45:15,913 - INFO - Successfully created agent 'loadtest-agent-002' with ID: a4763f5e-7aab-4f97-96f2-4a2d80e10bf8
2025-07-18 09:45:15,913 - INFO - Saved API key to apikeys.txt
2025-07-18 09:45:15,913 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:45:15,913 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-18 09:45:15,982 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: a8742dad-b443-4842-9938-757bd9795cfd
2025-07-18 09:45:15,982 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream01
2025-07-18 09:45:16,483 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-18 09:45:16,567 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: d8fa64fa-573b-4f9d-98ec-666286808f07
2025-07-18 09:45:16,567 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream02
2025-07-18 09:45:17,068 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-18 09:45:17,138 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: 4d21ea78-33fa-43a8-ab52-c0751d94d20e
2025-07-18 09:45:17,138 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream03
2025-07-18 09:45:17,639 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-18 09:45:17,706 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: f801543c-a75a-4217-ad69-3f40cc7f1b7a
2025-07-18 09:45:17,706 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream04
2025-07-18 09:45:18,207 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-18 09:45:18,286 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: f63c66f4-8ce5-4158-b571-f0f82f9ce2ea
2025-07-18 09:45:18,286 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream05
2025-07-18 09:45:18,786 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-18 09:45:19,287 - INFO - Setting up agent 3/5
2025-07-18 09:45:19,287 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-18 09:45:19,350 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 1bb6375f-78c1-4f37-9add-b32dc54825f3
2025-07-18 09:45:19,350 - INFO - Saved API key to apikeys.txt
2025-07-18 09:45:19,350 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:45:19,350 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-18 09:45:19,481 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: 24ef62c8-ffe8-4d03-981c-b5c760f3507b
2025-07-18 09:45:19,481 - INFO - Camera 'load-test-camera-loadtest-agent-003-001' created with RTSP stream01
2025-07-18 09:45:19,982 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-18 09:45:20,059 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: 4891de8f-30be-4bda-b619-a6e7f958bb15
2025-07-18 09:45:20,060 - INFO - Camera 'load-test-camera-loadtest-agent-003-002' created with RTSP stream02
2025-07-18 09:45:20,560 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-18 09:45:20,634 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: df9fb02d-c48e-45ef-a4da-3d0cedfd0863
2025-07-18 09:45:20,634 - INFO - Camera 'load-test-camera-loadtest-agent-003-003' created with RTSP stream03
2025-07-18 09:45:21,135 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-18 09:45:21,215 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 91f18f1d-29a2-413a-a4a1-929fde94470b
2025-07-18 09:45:21,215 - INFO - Camera 'load-test-camera-loadtest-agent-003-004' created with RTSP stream04
2025-07-18 09:45:21,716 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-18 09:45:21,785 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 1416c911-46ae-40b4-b30b-4222d57b9a4c
2025-07-18 09:45:21,786 - INFO - Camera 'load-test-camera-loadtest-agent-003-005' created with RTSP stream05
2025-07-18 09:45:22,286 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-18 09:45:22,787 - INFO - Setting up agent 4/5
2025-07-18 09:45:22,787 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-18 09:45:22,849 - INFO - Successfully created agent 'loadtest-agent-004' with ID: b88b356a-34b8-492d-bdb8-ab61f985c35e
2025-07-18 09:45:22,850 - INFO - Saved API key to apikeys.txt
2025-07-18 09:45:22,851 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:45:22,851 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-18 09:45:22,925 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 84956f2c-6813-46f4-b793-5946925abebb
2025-07-18 09:45:22,925 - INFO - Camera 'load-test-camera-loadtest-agent-004-001' created with RTSP stream01
2025-07-18 09:45:23,426 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-18 09:45:23,502 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 865cbbd9-803a-4b8b-a17d-d815b310b5e6
2025-07-18 09:45:23,502 - INFO - Camera 'load-test-camera-loadtest-agent-004-002' created with RTSP stream02
2025-07-18 09:45:24,003 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-18 09:45:24,071 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: edb84acf-46af-476f-bd0d-2674c7ec06a1
2025-07-18 09:45:24,071 - INFO - Camera 'load-test-camera-loadtest-agent-004-003' created with RTSP stream03
2025-07-18 09:45:24,572 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-18 09:45:24,635 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: 36cb40df-3cfa-4877-ae9b-3890aa1fbb70
2025-07-18 09:45:24,636 - INFO - Camera 'load-test-camera-loadtest-agent-004-004' created with RTSP stream04
2025-07-18 09:45:25,136 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-18 09:45:25,211 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 24bb71fb-8311-4f15-8a8b-feda097a4fc0
2025-07-18 09:45:25,211 - INFO - Camera 'load-test-camera-loadtest-agent-004-005' created with RTSP stream05
2025-07-18 09:45:25,712 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-18 09:45:26,213 - INFO - Setting up agent 5/5
2025-07-18 09:45:26,214 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-18 09:45:26,287 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 7f32549b-4da0-4f89-ad0b-453608247b8a
2025-07-18 09:45:26,287 - INFO - Saved API key to apikeys.txt
2025-07-18 09:45:26,287 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 09:45:26,287 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-18 09:45:26,361 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 168c177b-af42-4202-b807-5a2b04e8fc4d
2025-07-18 09:45:26,362 - INFO - Camera 'load-test-camera-loadtest-agent-005-001' created with RTSP stream01
2025-07-18 09:45:26,862 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-18 09:45:26,928 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: 12bdcfcb-a603-40f9-9cf0-a7194c0322d0
2025-07-18 09:45:26,929 - INFO - Camera 'load-test-camera-loadtest-agent-005-002' created with RTSP stream02
2025-07-18 09:45:27,430 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-18 09:45:27,515 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 6026b05b-ba9f-463a-bde3-151feb910f81
2025-07-18 09:45:27,515 - INFO - Camera 'load-test-camera-loadtest-agent-005-003' created with RTSP stream03
2025-07-18 09:45:28,016 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-18 09:45:28,102 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: c808fa3a-4af7-4d7a-a6dd-adeef5b607da
2025-07-18 09:45:28,103 - INFO - Camera 'load-test-camera-loadtest-agent-005-004' created with RTSP stream04
2025-07-18 09:45:28,603 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-18 09:45:28,678 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 750716e6-5e38-4bcb-8a76-a0a73649a747
2025-07-18 09:45:28,679 - INFO - Camera 'load-test-camera-loadtest-agent-005-005' created with RTSP stream05
2025-07-18 09:45:29,180 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-18 09:45:29,180 - INFO - Setup complete: 5/5 agents created successfully
2025-07-18 09:45:29,180 - INFO - All API keys saved to apikeys.txt
2025-07-18 09:45:29,180 - INFO - All agent IDs saved to agent_ids.txt
2025-07-18 09:45:29,180 - INFO - Load test setup completed successfully!
2025-07-18 11:17:15,483 - INFO - Starting load test setup: 2 agents with 5 cameras each
2025-07-18 11:17:15,483 - INFO - Attempting login (attempt 1)
2025-07-18 11:17:15,679 - INFO - Login successful! Token expires in 3600 seconds
2025-07-18 11:17:15,679 - INFO - Cleared existing apikeys.txt
2025-07-18 11:17:15,680 - INFO - Cleared existing agent_ids.txt
2025-07-18 11:17:15,680 - INFO - Setting up agent 1/2
2025-07-18 11:17:15,680 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-18 11:17:15,789 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 1592355b-fd63-41a4-925d-d3ec8bdd6730
2025-07-18 11:17:15,792 - INFO - Saved API key to apikeys.txt
2025-07-18 11:17:15,793 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 11:17:15,793 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-1' (attempt 1)
2025-07-18 11:17:15,868 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-1' with ID: b17f8968-9972-4750-a296-cecfc9e07234
2025-07-18 11:17:15,868 - INFO - Camera 'load-test-camera-loadtest-agent-001-1' created with RTSP stream01
2025-07-18 11:17:16,369 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-2' (attempt 1)
2025-07-18 11:17:16,445 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-2' with ID: d758d2a2-d318-4de5-ba48-537d28233357
2025-07-18 11:17:16,446 - INFO - Camera 'load-test-camera-loadtest-agent-001-2' created with RTSP stream02
2025-07-18 11:17:16,948 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-3' (attempt 1)
2025-07-18 11:17:17,027 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-3' with ID: 2d392778-0404-4722-886e-79d8bd66d4b7
2025-07-18 11:17:17,027 - INFO - Camera 'load-test-camera-loadtest-agent-001-3' created with RTSP stream03
2025-07-18 11:17:17,528 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-4' (attempt 1)
2025-07-18 11:17:17,601 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-4' with ID: 2617f9b6-a397-43a8-9816-ce4916d4a427
2025-07-18 11:17:17,601 - INFO - Camera 'load-test-camera-loadtest-agent-001-4' created with RTSP stream04
2025-07-18 11:17:18,102 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-5' (attempt 1)
2025-07-18 11:17:18,174 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-5' with ID: 8bc8e82b-53fb-4262-8124-089b20a03bdb
2025-07-18 11:17:18,174 - INFO - Camera 'load-test-camera-loadtest-agent-001-5' created with RTSP stream05
2025-07-18 11:17:18,675 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-18 11:17:19,175 - INFO - Setting up agent 2/2
2025-07-18 11:17:19,176 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-18 11:17:19,251 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 3808ef83-3625-4e51-b1d2-19019153449c
2025-07-18 11:17:19,252 - INFO - Saved API key to apikeys.txt
2025-07-18 11:17:19,252 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 11:17:19,252 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-1' (attempt 1)
2025-07-18 11:17:19,317 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-1' with ID: 5a82f2a7-feda-48ea-b362-4504fd236693
2025-07-18 11:17:19,317 - INFO - Camera 'load-test-camera-loadtest-agent-002-1' created with RTSP stream01
2025-07-18 11:17:19,817 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-2' (attempt 1)
2025-07-18 11:17:19,892 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-2' with ID: fb689cf7-bf2c-41f5-9802-822363baef43
2025-07-18 11:17:19,892 - INFO - Camera 'load-test-camera-loadtest-agent-002-2' created with RTSP stream02
2025-07-18 11:17:20,393 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-3' (attempt 1)
2025-07-18 11:17:20,474 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-3' with ID: 330e2f89-7e3f-43ef-bc9c-3fa7d82d16ff
2025-07-18 11:17:20,475 - INFO - Camera 'load-test-camera-loadtest-agent-002-3' created with RTSP stream03
2025-07-18 11:17:20,975 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-4' (attempt 1)
2025-07-18 11:17:21,046 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-4' with ID: a515efd1-8fc3-4be6-9d38-0471ccc4985a
2025-07-18 11:17:21,046 - INFO - Camera 'load-test-camera-loadtest-agent-002-4' created with RTSP stream04
2025-07-18 11:17:21,547 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-5' (attempt 1)
2025-07-18 11:17:21,618 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-5' with ID: 8fc559cb-bf54-4895-9659-612c90c36316
2025-07-18 11:17:21,618 - INFO - Camera 'load-test-camera-loadtest-agent-002-5' created with RTSP stream05
2025-07-18 11:17:22,119 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-18 11:17:22,119 - INFO - Setup complete: 2/2 agents created successfully
2025-07-18 11:17:22,119 - INFO - All API keys saved to apikeys.txt
2025-07-18 11:17:22,119 - INFO - All agent IDs saved to agent_ids.txt
2025-07-18 11:17:22,119 - INFO - Load test setup completed successfully!
2025-07-18 11:19:02,254 - INFO - Starting load test setup: 2 agents with 5 cameras each
2025-07-18 11:19:02,254 - INFO - Attempting login (attempt 1)
2025-07-18 11:19:02,460 - INFO - Login successful! Token expires in 3600 seconds
2025-07-18 11:19:02,461 - INFO - Cleared existing apikeys.txt
2025-07-18 11:19:02,461 - INFO - Cleared existing agent_ids.txt
2025-07-18 11:19:02,461 - INFO - Setting up agent 1/2
2025-07-18 11:19:02,462 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-18 11:19:02,584 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 5b32b792-29aa-4f43-858d-cc687f168e6c
2025-07-18 11:19:02,585 - INFO - Saved API key to apikeys.txt
2025-07-18 11:19:02,587 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 11:19:02,587 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-18 11:19:02,662 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: d2e6180a-b7be-423d-a8ab-fe187bba9386
2025-07-18 11:19:02,662 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-18 11:19:03,163 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-18 11:19:03,235 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 02d6a0f5-ad37-45ba-a20a-c7d3795738cd
2025-07-18 11:19:03,235 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-18 11:19:03,736 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-18 11:19:03,802 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: e2864723-ba1f-42a3-b6cc-08a15a56af90
2025-07-18 11:19:03,802 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-18 11:19:04,303 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-18 11:19:04,369 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 42150dfb-7be7-4a82-9898-42217ff8016e
2025-07-18 11:19:04,369 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-18 11:19:04,870 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-18 11:19:04,939 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: f0688a3a-38f9-4eb7-9175-258223260721
2025-07-18 11:19:04,940 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-18 11:19:05,440 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-18 11:19:05,941 - INFO - Setting up agent 2/2
2025-07-18 11:19:05,941 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-18 11:19:05,996 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 38b3603c-86ea-44b4-b5cc-33e57b424b3c
2025-07-18 11:19:05,997 - INFO - Saved API key to apikeys.txt
2025-07-18 11:19:05,997 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 11:19:05,997 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-18 11:19:06,057 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 0ca61d0e-a84b-44af-980d-d30bc9f15513
2025-07-18 11:19:06,057 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream1
2025-07-18 11:19:06,558 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-18 11:19:06,637 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 3d332d3f-77a7-45da-8426-a7128340876c
2025-07-18 11:19:06,637 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream2
2025-07-18 11:19:07,138 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-18 11:19:07,204 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: 3b07fcf1-15e1-4875-8970-e626707063c6
2025-07-18 11:19:07,204 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream3
2025-07-18 11:19:07,705 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-18 11:19:07,781 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: 23d7abbb-37bb-443a-ac8a-534ad9772e54
2025-07-18 11:19:07,782 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream4
2025-07-18 11:19:08,284 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-18 11:19:08,344 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: be94c567-a51c-4bb2-b137-d293ec1f431e
2025-07-18 11:19:08,344 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream5
2025-07-18 11:19:08,845 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-18 11:19:08,845 - INFO - Setup complete: 2/2 agents created successfully
2025-07-18 11:19:08,845 - INFO - All API keys saved to apikeys.txt
2025-07-18 11:19:08,845 - INFO - All agent IDs saved to agent_ids.txt
2025-07-18 11:19:08,845 - INFO - Load test setup completed successfully!
2025-07-18 15:25:52,854 - INFO - Starting load test setup: 10 agents with 5 cameras each
2025-07-18 15:25:52,856 - INFO - Attempting login (attempt 1)
2025-07-18 15:25:53,003 - INFO - Login successful! Token expires in 3600 seconds
2025-07-18 15:25:53,004 - INFO - Cleared existing apikeys.txt
2025-07-18 15:25:53,005 - INFO - Cleared existing agent_ids.txt
2025-07-18 15:25:53,005 - INFO - Setting up agent 1/10
2025-07-18 15:25:53,005 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-18 15:25:53,126 - INFO - Successfully created agent 'loadtest-agent-001' with ID: df61db9a-d44f-4954-b0db-6573d7155f5a
2025-07-18 15:25:53,129 - INFO - Saved API key to apikeys.txt
2025-07-18 15:25:53,131 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:25:53,131 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-18 15:25:53,198 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 15042d3b-337e-4565-9845-013634840dc3
2025-07-18 15:25:53,199 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-18 15:25:53,699 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-18 15:25:53,782 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 09a3b552-bdc3-4dc5-85b0-2156c9152c47
2025-07-18 15:25:53,782 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-18 15:25:54,283 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-18 15:25:54,360 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: cc5cf19f-a854-408e-ab6e-42bcaddeb73e
2025-07-18 15:25:54,360 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-18 15:25:54,861 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-18 15:25:54,944 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 919776c5-a147-4ed0-a9ff-5a8712327192
2025-07-18 15:25:54,944 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-18 15:25:55,445 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-18 15:25:55,528 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: d8153d7d-0b4f-49ec-9534-6c1259d3f6f7
2025-07-18 15:25:55,529 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-18 15:25:56,031 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-18 15:25:56,532 - INFO - Setting up agent 2/10
2025-07-18 15:25:56,532 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-18 15:25:56,610 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 931f94ae-19f6-44ee-8540-efd076bf71ed
2025-07-18 15:25:56,611 - INFO - Saved API key to apikeys.txt
2025-07-18 15:25:56,612 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:25:56,612 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-18 15:25:56,683 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 5633c48f-d3ea-4ce3-9a7b-e95cd2328625
2025-07-18 15:25:56,683 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream1
2025-07-18 15:25:57,184 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-18 15:25:57,249 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: fe7c311c-4861-4ca7-bcdb-6a9bbea9fd3d
2025-07-18 15:25:57,249 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream2
2025-07-18 15:25:57,750 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-18 15:25:57,821 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: cc8499de-0743-4773-bef8-8bbe89233f6d
2025-07-18 15:25:57,821 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream3
2025-07-18 15:25:58,322 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-18 15:25:58,394 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: c98450e0-50fb-4c09-acb3-7086f95e0c33
2025-07-18 15:25:58,395 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream4
2025-07-18 15:25:58,895 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-18 15:25:58,959 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 9026e42a-6cd0-4ce8-b377-ecffc020806f
2025-07-18 15:25:58,959 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream5
2025-07-18 15:25:59,460 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-18 15:25:59,961 - INFO - Setting up agent 3/10
2025-07-18 15:25:59,961 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-18 15:26:00,031 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 20174240-871e-4d8d-9b1b-71bf488420f8
2025-07-18 15:26:00,032 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:00,033 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:00,033 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-18 15:26:00,095 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: dc3bf718-68d0-490f-8e98-048651d777b3
2025-07-18 15:26:00,095 - INFO - Camera 'load-test-camera-loadtest-agent-003-001' created with RTSP stream1
2025-07-18 15:26:00,596 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-18 15:26:00,669 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: db3db729-2a96-4918-b861-27e657855d70
2025-07-18 15:26:00,669 - INFO - Camera 'load-test-camera-loadtest-agent-003-002' created with RTSP stream2
2025-07-18 15:26:01,170 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-18 15:26:01,235 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: 8c53e11b-c12d-4e2f-abb9-edacab1166de
2025-07-18 15:26:01,235 - INFO - Camera 'load-test-camera-loadtest-agent-003-003' created with RTSP stream3
2025-07-18 15:26:01,736 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-18 15:26:02,160 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 7f001899-57ac-4c61-80dd-b63fc65bf943
2025-07-18 15:26:02,160 - INFO - Camera 'load-test-camera-loadtest-agent-003-004' created with RTSP stream4
2025-07-18 15:26:02,661 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-18 15:26:02,851 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 96d8b3ac-6d7c-416a-976f-31a194ddff2d
2025-07-18 15:26:02,851 - INFO - Camera 'load-test-camera-loadtest-agent-003-005' created with RTSP stream5
2025-07-18 15:26:03,352 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-18 15:26:03,853 - INFO - Setting up agent 4/10
2025-07-18 15:26:03,853 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-18 15:26:03,925 - INFO - Successfully created agent 'loadtest-agent-004' with ID: ed530a75-5e3b-4c93-8ed6-61ccd042bbf2
2025-07-18 15:26:03,925 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:03,926 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:03,926 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-18 15:26:03,993 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 08ca9e75-590b-44a6-85d5-47f3bd623ead
2025-07-18 15:26:03,994 - INFO - Camera 'load-test-camera-loadtest-agent-004-001' created with RTSP stream1
2025-07-18 15:26:04,495 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-18 15:26:04,575 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 8322f4c2-fc26-4830-8889-3b4571eeb375
2025-07-18 15:26:04,575 - INFO - Camera 'load-test-camera-loadtest-agent-004-002' created with RTSP stream2
2025-07-18 15:26:05,076 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-18 15:26:05,152 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: e2469195-f002-451b-b0ec-769f01feb061
2025-07-18 15:26:05,152 - INFO - Camera 'load-test-camera-loadtest-agent-004-003' created with RTSP stream3
2025-07-18 15:26:05,653 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-18 15:26:05,728 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: a598e639-87cc-4e45-9d12-6ea1e0962c38
2025-07-18 15:26:05,729 - INFO - Camera 'load-test-camera-loadtest-agent-004-004' created with RTSP stream4
2025-07-18 15:26:06,230 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-18 15:26:06,299 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 1a1adbd0-2d6d-4bea-b3a3-33d06beb763e
2025-07-18 15:26:06,300 - INFO - Camera 'load-test-camera-loadtest-agent-004-005' created with RTSP stream5
2025-07-18 15:26:06,801 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-18 15:26:07,302 - INFO - Setting up agent 5/10
2025-07-18 15:26:07,302 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-18 15:26:07,375 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 6f3fe891-d3d4-496e-92b7-4cbacd15c648
2025-07-18 15:26:07,377 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:07,379 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:07,379 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-18 15:26:07,445 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 850c8ab7-852d-4d72-a24b-127c11a794e7
2025-07-18 15:26:07,445 - INFO - Camera 'load-test-camera-loadtest-agent-005-001' created with RTSP stream1
2025-07-18 15:26:07,946 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-18 15:26:08,013 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: 8ee85016-5ec6-4d20-9340-c2eedcfae330
2025-07-18 15:26:08,014 - INFO - Camera 'load-test-camera-loadtest-agent-005-002' created with RTSP stream2
2025-07-18 15:26:08,514 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-18 15:26:08,590 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 878a948f-61e5-4b5c-8aaa-502a02faaa78
2025-07-18 15:26:08,590 - INFO - Camera 'load-test-camera-loadtest-agent-005-003' created with RTSP stream3
2025-07-18 15:26:09,091 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-18 15:26:09,164 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: e66b0099-21e1-473a-8ca5-d86507edff20
2025-07-18 15:26:09,164 - INFO - Camera 'load-test-camera-loadtest-agent-005-004' created with RTSP stream4
2025-07-18 15:26:09,665 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-18 15:26:09,922 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: b2e44104-d95e-4797-bc87-a0ecce16b797
2025-07-18 15:26:09,923 - INFO - Camera 'load-test-camera-loadtest-agent-005-005' created with RTSP stream5
2025-07-18 15:26:10,424 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-18 15:26:10,925 - INFO - Setting up agent 6/10
2025-07-18 15:26:10,925 - INFO - Creating agent 'loadtest-agent-006' (attempt 1)
2025-07-18 15:26:10,996 - INFO - Successfully created agent 'loadtest-agent-006' with ID: 0c10a485-6327-4c7b-814b-aa7754efb502
2025-07-18 15:26:10,997 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:10,999 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:11,000 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-001' (attempt 1)
2025-07-18 15:26:11,092 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-001' with ID: a26e0185-7e7d-4f8a-a2c4-b414116803e7
2025-07-18 15:26:11,092 - INFO - Camera 'load-test-camera-loadtest-agent-006-001' created with RTSP stream1
2025-07-18 15:26:11,593 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-002' (attempt 1)
2025-07-18 15:26:11,673 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-002' with ID: f96a56e0-7dfd-41f2-ab2c-83eb154b5ccc
2025-07-18 15:26:11,674 - INFO - Camera 'load-test-camera-loadtest-agent-006-002' created with RTSP stream2
2025-07-18 15:26:12,174 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-003' (attempt 1)
2025-07-18 15:26:12,244 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-003' with ID: 7f059d80-ca59-470a-8251-2aecfcbe4df4
2025-07-18 15:26:12,244 - INFO - Camera 'load-test-camera-loadtest-agent-006-003' created with RTSP stream3
2025-07-18 15:26:12,746 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-004' (attempt 1)
2025-07-18 15:26:12,828 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-004' with ID: c18dd724-2aad-4b97-a498-3581f5886481
2025-07-18 15:26:12,828 - INFO - Camera 'load-test-camera-loadtest-agent-006-004' created with RTSP stream4
2025-07-18 15:26:13,329 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-005' (attempt 1)
2025-07-18 15:26:13,401 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-005' with ID: c143cf43-af2a-4a07-9202-3580f4449585
2025-07-18 15:26:13,401 - INFO - Camera 'load-test-camera-loadtest-agent-006-005' created with RTSP stream5
2025-07-18 15:26:13,902 - INFO - Successfully setup agent 'loadtest-agent-006' with 5 cameras
2025-07-18 15:26:14,402 - INFO - Setting up agent 7/10
2025-07-18 15:26:14,403 - INFO - Creating agent 'loadtest-agent-007' (attempt 1)
2025-07-18 15:26:14,483 - INFO - Successfully created agent 'loadtest-agent-007' with ID: 4d2d50a9-f222-4f15-8500-faf81865dc52
2025-07-18 15:26:14,483 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:14,483 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:14,483 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-001' (attempt 1)
2025-07-18 15:26:14,552 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-001' with ID: 34ceed78-8f52-41f4-a001-f262bde05aeb
2025-07-18 15:26:14,552 - INFO - Camera 'load-test-camera-loadtest-agent-007-001' created with RTSP stream1
2025-07-18 15:26:15,053 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-002' (attempt 1)
2025-07-18 15:26:15,123 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-002' with ID: 110083cf-7cc8-4c09-992d-81346a909ada
2025-07-18 15:26:15,123 - INFO - Camera 'load-test-camera-loadtest-agent-007-002' created with RTSP stream2
2025-07-18 15:26:15,624 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-003' (attempt 1)
2025-07-18 15:26:15,698 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-003' with ID: 7edff454-74b0-43ff-a756-f195f4ef0eda
2025-07-18 15:26:15,698 - INFO - Camera 'load-test-camera-loadtest-agent-007-003' created with RTSP stream3
2025-07-18 15:26:16,199 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-004' (attempt 1)
2025-07-18 15:26:16,276 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-004' with ID: 0d7c9a84-137a-4e1d-ab8d-a088f3457da2
2025-07-18 15:26:16,276 - INFO - Camera 'load-test-camera-loadtest-agent-007-004' created with RTSP stream4
2025-07-18 15:26:16,777 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-005' (attempt 1)
2025-07-18 15:26:16,844 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-005' with ID: f4c180b0-3860-48e3-95d3-13575b10ddc5
2025-07-18 15:26:16,844 - INFO - Camera 'load-test-camera-loadtest-agent-007-005' created with RTSP stream5
2025-07-18 15:26:17,345 - INFO - Successfully setup agent 'loadtest-agent-007' with 5 cameras
2025-07-18 15:26:17,846 - INFO - Setting up agent 8/10
2025-07-18 15:26:17,846 - INFO - Creating agent 'loadtest-agent-008' (attempt 1)
2025-07-18 15:26:17,922 - INFO - Successfully created agent 'loadtest-agent-008' with ID: 2af5cafa-1f09-487b-83ed-b67050d736dc
2025-07-18 15:26:17,922 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:17,923 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:17,923 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-001' (attempt 1)
2025-07-18 15:26:17,990 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-001' with ID: 7c3afac6-2ca7-4418-94da-49ef3cc07b5e
2025-07-18 15:26:17,990 - INFO - Camera 'load-test-camera-loadtest-agent-008-001' created with RTSP stream1
2025-07-18 15:26:18,491 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-002' (attempt 1)
2025-07-18 15:26:18,559 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-002' with ID: d410826d-973a-4b2a-b7a3-89a8242f6a72
2025-07-18 15:26:18,559 - INFO - Camera 'load-test-camera-loadtest-agent-008-002' created with RTSP stream2
2025-07-18 15:26:19,060 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-003' (attempt 1)
2025-07-18 15:26:19,130 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-003' with ID: 2479c66d-21bc-4d91-99ab-0f5042a83ff4
2025-07-18 15:26:19,130 - INFO - Camera 'load-test-camera-loadtest-agent-008-003' created with RTSP stream3
2025-07-18 15:26:19,631 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-004' (attempt 1)
2025-07-18 15:26:19,715 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-004' with ID: c1b206b8-3512-4700-bcf1-2e5d3007d5e4
2025-07-18 15:26:19,715 - INFO - Camera 'load-test-camera-loadtest-agent-008-004' created with RTSP stream4
2025-07-18 15:26:20,216 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-005' (attempt 1)
2025-07-18 15:26:20,302 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-005' with ID: abec471d-6cf8-4c65-ae5c-bf0a4b14577c
2025-07-18 15:26:20,302 - INFO - Camera 'load-test-camera-loadtest-agent-008-005' created with RTSP stream5
2025-07-18 15:26:20,803 - INFO - Successfully setup agent 'loadtest-agent-008' with 5 cameras
2025-07-18 15:26:21,304 - INFO - Setting up agent 9/10
2025-07-18 15:26:21,304 - INFO - Creating agent 'loadtest-agent-009' (attempt 1)
2025-07-18 15:26:21,382 - INFO - Successfully created agent 'loadtest-agent-009' with ID: 262c1d69-3211-4d48-8a8c-2d3d73450287
2025-07-18 15:26:21,383 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:21,384 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:21,384 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-001' (attempt 1)
2025-07-18 15:26:21,560 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-001' with ID: 5aba467a-ab56-434d-9359-f247be1aeaa6
2025-07-18 15:26:21,561 - INFO - Camera 'load-test-camera-loadtest-agent-009-001' created with RTSP stream1
2025-07-18 15:26:22,061 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-002' (attempt 1)
2025-07-18 15:26:22,131 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-002' with ID: 6751e482-2599-45c2-b4e4-efc41cc2252c
2025-07-18 15:26:22,131 - INFO - Camera 'load-test-camera-loadtest-agent-009-002' created with RTSP stream2
2025-07-18 15:26:22,632 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-003' (attempt 1)
2025-07-18 15:26:22,711 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-003' with ID: dbd3fe67-bb29-456d-9761-d0c978a327ef
2025-07-18 15:26:22,711 - INFO - Camera 'load-test-camera-loadtest-agent-009-003' created with RTSP stream3
2025-07-18 15:26:23,212 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-004' (attempt 1)
2025-07-18 15:26:23,289 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-004' with ID: 7fca9434-775f-4a4c-9104-ac96d93570fa
2025-07-18 15:26:23,290 - INFO - Camera 'load-test-camera-loadtest-agent-009-004' created with RTSP stream4
2025-07-18 15:26:23,792 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-005' (attempt 1)
2025-07-18 15:26:23,861 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-005' with ID: 2d9eddef-33ee-423e-9f4b-7d0bd7967423
2025-07-18 15:26:23,861 - INFO - Camera 'load-test-camera-loadtest-agent-009-005' created with RTSP stream5
2025-07-18 15:26:24,362 - INFO - Successfully setup agent 'loadtest-agent-009' with 5 cameras
2025-07-18 15:26:24,863 - INFO - Setting up agent 10/10
2025-07-18 15:26:24,863 - INFO - Creating agent 'loadtest-agent-010' (attempt 1)
2025-07-18 15:26:24,929 - INFO - Successfully created agent 'loadtest-agent-010' with ID: 5c73a8aa-1c6d-4cfd-a6ea-cccfd85596c2
2025-07-18 15:26:24,930 - INFO - Saved API key to apikeys.txt
2025-07-18 15:26:24,931 - INFO - Saved agent ID to agent_ids.txt
2025-07-18 15:26:24,931 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-001' (attempt 1)
2025-07-18 15:26:25,000 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-001' with ID: 0eeea2f9-4ebf-42bf-982b-547e75a17662
2025-07-18 15:26:25,000 - INFO - Camera 'load-test-camera-loadtest-agent-010-001' created with RTSP stream1
2025-07-18 15:26:25,501 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-002' (attempt 1)
2025-07-18 15:26:25,561 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-002' with ID: 99e5ba60-8c41-4ed7-9a87-e80632eb9400
2025-07-18 15:26:25,561 - INFO - Camera 'load-test-camera-loadtest-agent-010-002' created with RTSP stream2
2025-07-18 15:26:26,062 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-003' (attempt 1)
2025-07-18 15:26:26,127 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-003' with ID: 50fd082a-7295-4cfe-97ba-b33df772532e
2025-07-18 15:26:26,127 - INFO - Camera 'load-test-camera-loadtest-agent-010-003' created with RTSP stream3
2025-07-18 15:26:26,628 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-004' (attempt 1)
2025-07-18 15:26:26,706 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-004' with ID: f26206c5-1b7b-4405-b07a-7345602775ae
2025-07-18 15:26:26,706 - INFO - Camera 'load-test-camera-loadtest-agent-010-004' created with RTSP stream4
2025-07-18 15:26:27,207 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-005' (attempt 1)
2025-07-18 15:26:27,287 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-005' with ID: 30712b8e-8807-4c5d-9aba-83ec071ab78e
2025-07-18 15:26:27,288 - INFO - Camera 'load-test-camera-loadtest-agent-010-005' created with RTSP stream5
2025-07-18 15:26:27,789 - INFO - Successfully setup agent 'loadtest-agent-010' with 5 cameras
2025-07-18 15:26:27,789 - INFO - Setup complete: 10/10 agents created successfully
2025-07-18 15:26:27,789 - INFO - All API keys saved to apikeys.txt
2025-07-18 15:26:27,789 - INFO - All agent IDs saved to agent_ids.txt
2025-07-18 15:26:27,789 - INFO - Load test setup completed successfully!
