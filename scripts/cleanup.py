#!/usr/bin/env python3
"""
Performs cleanup of resources created by load test setup
- Calls API to remove agents by agent ID
- Reads agent IDs from agent_ids.txt file created by setup script
"""

"""
Example of DELETE /agents
https://api.volks-stg.knizsoft.com/api/v1/agents/10d5c903-a022-4cb0-95bf-588d4db1e947
https://api.volks-stg.knizsoft.com/api/v1/agents/<agent_id>

Expected response from DELETE /agents/<agent_id>
{
    "code": 0,
    "message": "Success"
}
"""

import requests
import time
import logging
import argparse
from typing import List

# Environment-specific configuration
ENVIRONMENTS = {
    'dev': {
        'api_base_url': "https://api.volks-dev.knizsoft.com/api/v1",
        'login_endpoint': "https://auth.volks-dev.knizsoft.com/token?grant_type=password"
    },
    'stg': {
        'api_base_url': "https://api.volks-stg.knizsoft.com/api/v1",
        'login_endpoint': "https://auth.volks-stg.knizsoft.com/token?grant_type=password"
    }
}

API_AGENT = "/agents"

# Login Info
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "123456aA@"

# File paths
AGENT_IDS_FILE = "agent_ids.txt"
REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 3
DELAY_BETWEEN_REQUESTS = 0.5  # seconds

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('load_test_cleanup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class LoadTestCleanup:
    """Handles cleanup of load test resources"""

    def __init__(self, environment: str = 'dev'):
        if environment not in ENVIRONMENTS:
            raise ValueError(f"Invalid environment: {environment}. Must be one of: {list(ENVIRONMENTS.keys())}")

        self.env_config = ENVIRONMENTS[environment]
        self.api_base_url = self.env_config['api_base_url'].rstrip('/')
        self.login_endpoint = self.env_config['login_endpoint']

        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'LoadTestCleanup/1.0'
        })
        self.bearer_token = None

    def login(self) -> bool:
        """
        Login to get bearer token for authentication

        Returns:
            True if login successful, False otherwise
        """
        payload = {
            "email": ADMIN_EMAIL,
            "password": ADMIN_PASSWORD
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Attempting login (attempt {attempt + 1})")
                response = self.session.post(self.login_endpoint, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                login_data = response.json()

                # Extract access token
                access_token = login_data.get('access_token')
                if not access_token:
                    logger.error("No access token returned from login")
                    return False

                self.bearer_token = access_token

                # Update session headers with bearer token
                self.session.headers.update({
                    'Authorization': f'Bearer {self.bearer_token}'
                })

                # Log token expiry info
                expires_in = login_data.get('expires_in', 'unknown')
                logger.info(f"Login successful! Token expires in {expires_in} seconds")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Login failed (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Login failed after {RETRY_ATTEMPTS} attempts")
        return False

    def delete_agent(self, agent_id: str) -> bool:
        """
        Delete an agent by ID

        Args:
            agent_id: ID of the agent to delete

        Returns:
            True if successful, False otherwise
        """
        url = f"{self.api_base_url}{API_AGENT}/{agent_id}"

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Deleting agent '{agent_id}' (attempt {attempt + 1})")
                response = self.session.delete(url, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                # Check if we got a JSON response
                try:
                    response_data = response.json()
                    # Check if API returned success
                    if response_data.get('code') != 0:
                        logger.error(f"API returned error for agent '{agent_id}': {response_data.get('message', 'Unknown error')}")
                        return False
                except ValueError:
                    # Some APIs might return empty response on successful delete
                    pass

                logger.info(f"Successfully deleted agent '{agent_id}'")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to delete agent '{agent_id}' (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to delete agent '{agent_id}' after {RETRY_ATTEMPTS} attempts")
        return False

    def load_agent_ids(self, filename: str = AGENT_IDS_FILE) -> List[str]:
        """
        Load agent IDs from file

        Args:
            filename: File containing agent IDs (one per line)

        Returns:
            List of agent IDs
        """
        try:
            with open(filename, 'r') as f:
                agent_ids = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(agent_ids)} agent IDs from {filename}")
            return agent_ids
        except FileNotFoundError:
            logger.error(f"Agent IDs file '{filename}' not found")
            return []
        except Exception as e:
            logger.error(f"Failed to load agent IDs from {filename}: {e}")
            return []

    def run_cleanup(self) -> bool:
        """
        Run the complete cleanup process

        Returns:
            True if all agents were deleted successfully, False otherwise
        """
        logger.info("Starting load test cleanup")

        # Step 1: Login to get bearer token
        if not self.login():
            logger.error("Failed to login - cannot proceed with cleanup")
            return False

        # Step 2: Load agent IDs
        agent_ids = self.load_agent_ids()
        if not agent_ids:
            logger.warning("No agent IDs found - nothing to clean up")
            return True

        # Step 3: Delete agents
        successful_deletions = 0
        failed_deletions = []

        for i, agent_id in enumerate(agent_ids, 1):
            logger.info(f"Deleting agent {i}/{len(agent_ids)}: {agent_id}")

            if self.delete_agent(agent_id):
                successful_deletions += 1
            else:
                failed_deletions.append(agent_id)

            # Small delay between deletions to avoid overwhelming the API
            if i < len(agent_ids):
                time.sleep(DELAY_BETWEEN_REQUESTS)

        # Summary
        logger.info(f"Cleanup complete: {successful_deletions}/{len(agent_ids)} agents deleted successfully")

        if failed_deletions:
            logger.warning(f"Failed to delete agents: {failed_deletions}")
            return False

        logger.info("All agents deleted successfully!")
        return True

    def cleanup(self):
        """Clean up resources"""
        self.session.close()


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description='Cleanup load test agents',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cleanup.py --env dev    # Cleanup agents from dev environment
  python cleanup.py --env stg    # Cleanup agents from staging environment
  python cleanup.py              # Cleanup agents from dev environment (default)

This script will:
1. Login to the specified environment API
2. Read agent IDs from agent_ids.txt
3. Delete each agent via API calls
        """
    )

    parser.add_argument(
        '--env',
        choices=['dev', 'stg'],
        default='dev',
        help='Environment to use (default: dev)'
    )

    args = parser.parse_args()

    cleanup = LoadTestCleanup(environment=args.env)

    try:
        success = cleanup.run_cleanup()
        if success:
            logger.info("Load test cleanup completed successfully!")
            print("\n✅ Cleanup complete! All agents have been deleted.")
            print(f"🌍 Environment: {args.env.upper()}")
            print(f"📊 Check {AGENT_IDS_FILE} if you need to verify which agents were processed.")
        else:
            logger.error("Load test cleanup failed!")
            print("\n❌ Cleanup failed! Check the logs for details.")
            print("Some agents may still exist and need manual cleanup.")
            return 1

    except KeyboardInterrupt:
        logger.info("Cleanup interrupted by user")
        print("\n⚠️  Cleanup interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {e}")
        print(f"\n💥 Unexpected error: {e}")
        return 1
    finally:
        cleanup.cleanup()

    return 0


if __name__ == "__main__":
    exit(main())